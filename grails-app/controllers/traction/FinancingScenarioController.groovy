package traction

import grails.plugin.springsecurity.annotation.Secured
import traction.bank.Bank
import traction.client.ClientInterest
import traction.cotation.Cotation
import traction.financingScenario.CotationFinancingScenario
import traction.financingScenario.FinancingScenario
import traction.financingScenario.FinancingScenarioValidation
import traction.financingScenario.FinancingScenarioValidationMake
import traction.render.RenderUtils

@Secured('ROLE_USER')
class FinancingScenarioController {
    def financingScenarioService
    def cotationService

    def deleteCotationScenario() {
        CotationFinancingScenario financingScenario = CotationFinancingScenario.get(params.long("id"))
        Cotation cotation = Cotation.findByFinancingScenario(financingScenario)
        if (!cotation && financingScenarioService.delete(financingScenario)) {
            return render(RenderUtils.success("cotation.scenario.success"))
        }
        render(RenderUtils.error("cotation.scenario.error"))
    }

    def delete() {
        FinancingScenario financingScenario = FinancingScenario.get(params.long("id"))
        financingScenarioService.delete(financingScenario)
    }

    def linkToCotation() {
        CotationFinancingScenario financingScenario = CotationFinancingScenario.get(params.long("scenarioId"))
        Cotation cotation = Cotation.get(params.long("cotationId"))
        if (cotation && financingScenario) {
            cotation.financingScenario = financingScenario
            if (cotationService.save(cotation)) {
                return render(RenderUtils.success("cotation.scenario.success"))
            }
        }
        render(RenderUtils.error("cotation.scenario.error"))
    }

    def update() {
        CotationFinancingScenario financingScenario = CotationFinancingScenario.get(params.long("id"))
        financingScenario.setNestedProperty(params.property, params.value)
        log.debug("update scenario", params.property, params.value)
        financingScenario = (CotationFinancingScenario) financingScenarioService.save(financingScenario)

        if (financingScenario) {
            return render(RenderUtils.json(true, "cotation.scenario.success", [scenarioTotal: financingScenario.calculate(), scenarioNetFinanced: financingScenario.netFinanced]))
        }
        render(RenderUtils.error("cotation.scenario.error"))
    }

    def save() {
        Cotation cotation = Cotation.get(params.long("cotationId"))
        if (cotation) {
            CotationFinancingScenario cotationFinancingScenario = new CotationFinancingScenario()
            cotationFinancingScenario.cotation = cotation
            cotationFinancingScenario.name = I18N.m('custom.scenario')
            cotationFinancingScenario.bank = Bank.first().selectedBank
            if (financingScenarioService.save(cotationFinancingScenario)) {
                return render(RenderUtils.success("cotation.scenario.success"))
            }
        } else {
            if (!params.bank || !params.interestRate || !params.termType || !params.term || !params.name) {
                render(RenderUtils.error("cotation.scenario.error"))
            }
            FinancingScenario financingScenario
            if (params.id) {
                financingScenario = FinancingScenario.get(params.id)
            } else {
                financingScenario = new FinancingScenario()
                FinancingScenarioValidation financingScenarioValidation = new FinancingScenarioValidation()
                financingScenario.validation = financingScenarioValidation
            }
            financingScenario.name = params.name
            financingScenario.bank = params.bank ? Bank.Banks.getById(Integer.parseInt(params.bank)) : Bank.first().selectedBank
            financingScenario.interestRate = NumberUtils.setDouble(params.interestRate)
            financingScenario.termType = FinancingScenario.TermType.getById(Integer.parseInt(params.termType))
            financingScenario.term = Integer.parseInt(params.term)
            financingScenario.guarantee = params.guarantee ? NumberUtils.setDouble(params.guarantee) : 0
            financingScenario.protection = params.protection ? NumberUtils.setDouble(params.protection) : 0
            financingScenario.insurance = params.insurance ? NumberUtils.setDouble(params.insurance) : 0
            financingScenario.validation.vehicleState = params.vehicleState
            financingScenario.validation.vehicleInterest = params.getList('vehicleType').collect { ClientInterest.get(it as long) }
            financingScenarioService.save(financingScenario)
            def newMakes = params.getList('vehicleMake') as List<String>

            def makesToRemove = financingScenario.validation.vehicleMake.findAll { !newMakes.contains(it.name) }
            makesToRemove.each {
                financingScenario.validation.removeFromVehicleMake(it)
                financingScenarioService.deleteMake(it)
            }

            newMakes.each {
                if (!FinancingScenarioValidationMake.findByNameAndValidation(it, financingScenario.validation)) {
                    financingScenario.validation.addToVehicleMake(new FinancingScenarioValidationMake(name: it))
                }
            }
            financingScenario.validation.vehicleYearMin = params.yearStart ? Integer.parseInt(params.yearStart) : null
            financingScenario.validation.vehicleYearMax = params.yearEnd ? Integer.parseInt(params.yearEnd) : null
            financingScenario.validation.vehiclePriceMin = params.priceMinimum ? NumberUtils.setDouble(params.priceMinimum) : null
            financingScenario.validation.vehiclePriceMax = params.priceMaximum ? NumberUtils.setDouble(params.priceMaximum) : null
            if (financingScenarioService.save(financingScenario)) {
                return render(RenderUtils.success("cotation.scenario.success"))
            }
        }

        render(RenderUtils.error("cotation.scenario.error"))
    }
}
