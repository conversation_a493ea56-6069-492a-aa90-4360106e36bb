package traction

import grails.plugin.springsecurity.annotation.Secured
import traction.client.Client
import traction.client.ClientService
import traction.render.RenderUtils
import traction.security.User
import traction.security.UserService

@Secured('ROLE_USER')
class ExternalIdentifierController {
    ExternalIdentifierService externalIdentifierService
    ClientService clientService
    UserService userService

    def add() {
        log.debug "addExtId:${params}"
        Client client = params.clientId ? Client.get(params.clientId as long) : null
        User user = params.userId ? User.get(params.userId as long) : null
        DMS dms = DMS.get(params.dmsId as long)
        String extId = params.extId
        def sameExtId = client ? Client.findByExtID(extId, dms) : (user ? User.findByExtID(extId, dms) : null)
        if (sameExtId) {
            return render(RenderUtils.error(client ? g.message(code:"client.flash.modify.extidexist", args:[sameExtId.getFullName()]) : g.message(code:"user.extID.exists", args:[sameExtId.username])))
        } else {
            if (client) {
                client.addToExternalIdentifiers(new ExternalIdentifier(extId: extId, dms: dms))
                client = clientService.save(client)
            } else if (user) {
                user.addToExternalIdentifiers(new ExternalIdentifier(extId: extId, dms: dms))
                user = userService.save(user)
            }
            if (client || user) {
                return render(RenderUtils.success("extID.added"))
            }
        }
        render(RenderUtils.error())
    }

    def delete() {
        log.debug "deleteExtId:${params}"
        ExternalIdentifier extId = ExternalIdentifier.get(params.id as long)
        if (extId) {
            if (externalIdentifierService.delete(extId)) {
                return render(RenderUtils.success("extID.removed"))
            }
        }
        render(RenderUtils.error())
    }

    def modalAddExtId() {
        log.debug "modalAddExtId:${params}"
        Client client = params.clientId ? Client.get(params.clientId as long) : null
        User user = params.userId ? User.get(params.userId as long) : null
        render(template: "modalAddExtId", model: [
                dmsList: DMS.getAll(),
                client: client,
                user: user
        ])
    }
}
