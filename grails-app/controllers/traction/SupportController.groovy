package traction

import grails.plugin.springsecurity.annotation.Secured

@Secured('ROLE_USER')
class SupportController {

    def index() {
        render(view: "index", model: [
                agents: [
                        [
                                name: "<PERSON>",
                                email: "<EMAIL>"
                        ],
                        [
                                name: "<PERSON><PERSON>",
                                email: "<EMAIL>"
                        ],
                        [
                                name: "<PERSON>",
                                email: "<EMAIL>"
                        ],
                        [
                                name: "<PERSON>",
                                email: "daves<PERSON><PERSON><PERSON>@tractiondk.com"
                        ],
                        [
                                name: "<PERSON><PERSON><PERSON>",
                                email: "<EMAIL>"
                        ],
                        [
                                name: "<PERSON>",
                                email: "jero<PERSON>@tractiondk.com"
                        ],
                        [
                                name: "<PERSON>",
                                email: "<EMAIL>"
                        ],
                        [
                                name: "<PERSON>",
                                email: "<EMAIL>"
                        ],
                        [
                                name: "<PERSON>",
                                email: "<EMAIL>"
                        ],
                        [
                                name: "<PERSON>",
                                email: "<EMAIL>"
                        ]
                ]
        ])
    }

}
