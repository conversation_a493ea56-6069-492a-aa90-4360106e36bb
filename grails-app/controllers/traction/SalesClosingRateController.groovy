package traction

import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.department.Department
import traction.department.DepartmentGroup
import traction.history.HistoryOpportunity
import traction.opportunity.Opportunity
import traction.security.User

import java.text.DateFormat
import java.text.SimpleDateFormat

//@Secured('ROLE_USER')
@Secured("@securityService.secured('PERM_SALES_CLOSING_RATE_REPORT')")
class SalesClosingRateController {

    def springSecurityService
    def historyService
    def salesClosingRateService

    def index() {
        List<User> users = [springSecurityService.currentUser]
        Date to = new Date()
        Date from = to - 90
        String fromToInput = "${from.format("MM/dd/yyyy")} - ${to.format("MM/dd/yyyy")}"

        List status = [
                [id: "", message: Opportunity.Category.ACTIVE.message]
        ] + Opportunity.Category.ACTIVE.status

        List<DepartmentGroup> departmentGroups = DepartmentGroup.findAllByUserGroup(UserGroup.SALES)
        List<Media> medias = Media.values()
        render(view: "index", model: [users: users, medias: medias, departmentGroups: departmentGroups, fromToInput: fromToInput, status: status])
    }

    def table() {
        List status = [
                [id: "", message: Opportunity.Category.ACTIVE.message]
        ] + Opportunity.Category.ACTIVE.status
        Calendar c = Calendar.getInstance()
        c.set(Calendar.MILLISECOND, 0)
        c.set(Calendar.SECOND, 0)
        c.set(Calendar.MINUTE, 0)
        c.set(Calendar.HOUR_OF_DAY, 0)
        c.set(Calendar.DAY_OF_MONTH, 1)
        List<Map> months = (0..11).collect { [
            name: c.getDisplayName(Calendar.MONTH, Calendar.SHORT, request.locale).capitalize() + " " + c.get(Calendar.YEAR),
            index: c.add(Calendar.MONTH, -1) ?: it
        ] }.reverse()
        render(view: "table", model: [months: months, status: status])
    }

    def dataTable() {
        log.debug "datatable params: ${params}"
        Calendar c = Calendar.getInstance()
        c.set(Calendar.MILLISECOND, 0)
        c.set(Calendar.SECOND, 0)
        c.set(Calendar.MINUTE, 0)
        c.set(Calendar.HOUR_OF_DAY, 0)
        c.set(Calendar.DAY_OF_MONTH, 1)
        List<Map> months = (0..(params.int("months") - 1)).collect { [
            name: c.getDisplayName(Calendar.MONTH, Calendar.SHORT, request.locale).capitalize() + " " + c.get(Calendar.YEAR),
            nameShort: c.getDisplayName(Calendar.MONTH, Calendar.SHORT, request.locale).capitalize(),
            index: c.add(Calendar.MONTH, -1) ?: it
        ] }
        render(template: "dataTable", model: [months: months, id: params.id, method: params.method])
    }

    def getData() {
        log.debug "params:${params}"

        String method = params.method
        List<Department> departments = Department.getAll()
        User user = User.get(params.long("user", 0))
        Department department = Department.get(params.long("department", 0))
        Media media = Media.getById(params.int("media", 0))
        List data = []
        Opportunity.Status status
        if (params.status) {
            status = Opportunity.Status.getById(Integer.valueOf(params.status))
        }
        String orderCol = params["columns[" + params["order[0][column]"] + "][data]"]
        String orderDir = params["order[0][dir]"]

        switch (method) {
            case "department":
                departments.each { dept ->
                    data.add(salesClosingRateService.getData("department", dept, null, media, status, request.locale))
                }
                break
            case "user":
                Set<User> users = department ? department.users : User.getAll()
                users = users.findAll { !it.deleted && it.userGroup == UserGroup.SALES }
                users.each { u ->
                    data.add(salesClosingRateService.getData("user", null, u, media, status, request.locale))
                }
                break
            case "media":
                Media.values().each { m ->
                    data.add(salesClosingRateService.getData("media", department, user, m, status, request.locale))
                }
                break
        }

        data.sort { orderCol == method ? it[orderCol] : it[orderCol].now }
        Map json = [
                draw: params.draw,
                data: orderDir == "asc" ? data : data.reverse()
        ]
        render json as JSON
    }

    def statsRatioUsers() {
        log.debug "statsUsers:" + params
        List<User> users = []
        if (params.userIds) {
            params.userIds.split(',').each {
                User user = User.get(it)
                if (user) {
                    users.add(user)
                }
            }
        }
        else {
            users.add(springSecurityService.currentUser)
        }
        Date from = Date.parse("yyyy-MM-dd", params.from)
        Date to = Date.parse("yyyy-MM-dd", params.to) + 1
        String interval = params.interval

        Opportunity.Status status
        if (params.statusId) {
            status = Opportunity.Status.getById(Integer.valueOf(params.statusId))
        }
        List<Media> medias = params.list("medias[]").collect { Media.getById(Integer.valueOf(it)) }
        List leaders = historyService.getSalesClosingLeaders(users, status, medias)

        render(template: "statsRatioUsers", model: [statusList: Opportunity.Category.ACTIVE.status, leaders: leaders, mediaIds: medias.id.join(","), medias: medias, allMedias: Media.values(), status: status, from: from, to: to, interval: interval, users: users])
    }

    def getUserLineChartData() {
        log.debug "getUserLineChartData:" + params
        JSONObject jsonData = new JSONObject()

        if (params.userIds && params.from && params.to) {
            boolean isMonthInterval = params.interval == "month"
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)

            boolean isCategoryActive = true
            boolean stacked = params.stacked == "true"
            Opportunity.Status status
            if (params.statusId) {
                status = Opportunity.Status.getById(Integer.valueOf(params.statusId))
            }
            if (status) {
                isCategoryActive = false
            }

            List<User> users = params.userIds.split(',').collect { User.get(it) }.findAll()
            List<Media> medias = params.medias ? params.medias.split(',').collect { Media.getById(Integer.valueOf(it)) } : null

            JSONArray labels = new JSONArray()
            JSONArray datasets = new JSONArray()

            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            if (fromCalendar < toCalendar) {
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)
                int calendarIncrementProperty = isMonthInterval ? Calendar.MONTH : Calendar.DATE
                int calendarIncrementAmount = isMonthInterval ? 1 : 7
                for (;;) {
                    if (isMonthInterval) {
                        DateFormat formatter = new SimpleDateFormat("MMMM yyyy", request.locale);
                        String s = formatter.format(fromCalendar.getTime())
                        labels.add(s.substring(0, 1).toUpperCase() + s.substring(1))
                    } else {
                        labels.add(fromCalendar.format(g.message(code: "dateFormat")))
                    }
                    fromCalendar.add(calendarIncrementProperty, calendarIncrementAmount)
                    if (fromCalendar > toCalendar) break
                }
                fromCalendar.setTime(from)
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)

                HistoryOpportunity.Type historyTypeEnter = isCategoryActive ? HistoryOpportunity.Type.ENTER_CATEGORY : HistoryOpportunity.Type.ENTER_STATUS
                HistoryOpportunity.Type historyTypeSold = isCategoryActive ? HistoryOpportunity.Type.EXIT_CATEGORY_SOLD : HistoryOpportunity.Type.EXIT_STATUS_SOLD

                users.each { user ->
                    JSONObject dataset = new JSONObject()
                    dataset.put("label", user.fullName)
                    dataset.put("fill", stacked)
                    dataset.put("backgroundColor", user.color + "dd")
                    dataset.put("borderColor", user.color)
                    dataset.put("borderCapStyle", "butt")

                    JSONArray data = new JSONArray()

                    Calendar start = fromCalendar.clone()
                    for (; ;) {
                        Date STARTMONTH = start.getTime()
                        start.add(calendarIncrementProperty, calendarIncrementAmount)
                        Date ENDMONTH = start.getTime()
                        List historyDatas = HistoryOpportunity.createCriteria().list {
                            gt("date", STARTMONTH)
                            lt("date", ENDMONTH)
                            gt("date", from)
                            lt("date", to)
                            eq("userSales", user)
                            or {
                                eq("type", historyTypeEnter)
                                eq("type", historyTypeSold)
                            }
                            if (isCategoryActive) {
                                eq("category", Opportunity.Category.ACTIVE)
                            }
                            else {
                                eq("status", status)
                            }
                            isNotNull("userSales")
                            if (users && users.size() > 1) {
                                or {
                                    users.each { u ->
                                        eq("userSales", u)
                                    }
                                }
                            }
                            if (medias) {
                                'in'("media", medias)
                            }
                            projections {
                                groupProperty("type")
                                rowCount()
                            }
                        }
                        List enterEntry = historyDatas.find { it[0] == historyTypeEnter }
                        int enter = enterEntry ? enterEntry[1] : 0

                        List soldEntry = historyDatas.find { it[0] == historyTypeSold }
                        int sold = soldEntry ? soldEntry[1] : 0

                        double rate = sold ? 100 : 0
                        if (enter) {
                            rate = sold * 100 / enter
                        }

                        data.add(Math.round(rate * 10) / 10)
                        if (start > toCalendar) break
                    }

                    dataset.put("data", data)
                    datasets.add(dataset)
                }
            }

            jsonData.put("labels", labels)
            jsonData.put("datasets", datasets)
        }
        render jsonData as JSON
    }

    def getStatusLineChartData() {
        log.debug "getStatusLineChartData:" + params
        JSONObject jsonData = new JSONObject()
        if (params.userIds && params.from && params.to) {
            boolean isMonthInterval = params.interval == "month"
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)

            List<User> users = params.userIds.split(',').collect { User.get(it) }.findAll()
            List<Media> medias = params.medias ? params.medias.split(',').collect { Media.getById(Integer.valueOf(it)) } : null

            JSONArray labels = new JSONArray()
            JSONArray datasets = new JSONArray()

            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            if (fromCalendar < toCalendar) {
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)
                int calendarIncrementProperty = isMonthInterval ? Calendar.MONTH : Calendar.DATE
                int calendarIncrementAmount = isMonthInterval ? 1 : 7

                for (;;) {
                    if (isMonthInterval) {
                        DateFormat formatter = new SimpleDateFormat("MMMM yyyy", request.locale);
                        String s = formatter.format(fromCalendar.getTime())
                        labels.add(s.substring(0, 1).toUpperCase() + s.substring(1))
                    } else {
                        labels.add(fromCalendar.format(g.message(code: "dateFormat")))
                    }
                    fromCalendar.add(calendarIncrementProperty, calendarIncrementAmount)
                    if (fromCalendar > toCalendar) break
                }
                fromCalendar.setTime(from)
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)

                Opportunity.Category.ACTIVE.status.each { Opportunity.Status status ->
                    JSONObject dataset = new JSONObject()
                    dataset.put("label", g.message(code: status.message))
                    dataset.put("fill", false)
                    String statusColor = String.format("#%06x", new Random().nextInt(0xffffff + 1))
                    dataset.put("backgroundColor", statusColor)
                    dataset.put("borderColor", statusColor)
                    dataset.put("borderCapStyle", "butt")

                    JSONArray dataSold = new JSONArray()
                    JSONArray dataRatio = new JSONArray()

                    Calendar start = fromCalendar.clone()
                    for (;;) {
                        Date STARTMONTH = start.getTime()
                        start.add(calendarIncrementProperty, calendarIncrementAmount)
                        Date ENDMONTH = start.getTime()
                        List historyDatas = HistoryOpportunity.createCriteria().list {
                            gt("date", STARTMONTH)
                            lt("date", ENDMONTH)
                            gt("date", from)
                            lt("date", to)
                            or {
                                eq("type", HistoryOpportunity.Type.ENTER_STATUS)
                                eq("type", HistoryOpportunity.Type.EXIT_STATUS_SOLD)
                            }
                            eq("status", status)
                            if (users) {
                                or {
                                    users.each { u ->
                                        eq("userSales", u)
                                    }
                                }
                            }
                            if (medias) {
                                'in'("media", medias)
                            }
                            projections {
                                groupProperty("type")
                                rowCount()
                            }
                        }
                        List enterEntry = historyDatas.find { it[0] == HistoryOpportunity.Type.ENTER_STATUS }
                        int enter = enterEntry ? enterEntry[1] : 0

                        List soldEntry = historyDatas.find { it[0] == HistoryOpportunity.Type.EXIT_STATUS_SOLD }
                        int sold = soldEntry ? soldEntry[1] : 0

                        double rate = sold ? 100 : 0
                        if (enter) {
                            rate = sold * 100 / enter
                        }

                        dataRatio.add(Math.round(rate * 10) / 10)
                        dataSold.add(sold)
                        if (start > toCalendar) break
                    }

                    dataset.put("ratio", dataRatio)
                    dataset.put("sold", dataSold)
                    datasets.add(dataset)
                }


                // Dataset for Opportunity.Category.ACTIVE
                JSONObject datasetCategory = new JSONObject()
                datasetCategory.put("label", g.message(code: Opportunity.Category.ACTIVE.message))
                datasetCategory.put("fill", false)
                datasetCategory.put("backgroundColor", "red")
                datasetCategory.put("borderColor", "red")
                datasetCategory.put("borderCapStyle", "butt")

                JSONArray dataRatioCategory = new JSONArray()
                JSONArray dataSoldCategory = new JSONArray()

                Calendar start = fromCalendar.clone()
                for (;;) {
                    Date STARTMONTH = start.getTime()
                    start.add(calendarIncrementProperty, calendarIncrementAmount)
                    Date ENDMONTH = start.getTime()
                    List historyDatas = HistoryOpportunity.createCriteria().list {
                        gt("date", STARTMONTH)
                        lt("date", ENDMONTH)
                        gt("date", from)
                        lt("date", to)
                        or {
                            eq("type", HistoryOpportunity.Type.ENTER_CATEGORY)
                            eq("type", HistoryOpportunity.Type.EXIT_CATEGORY_SOLD)
                        }
                        eq("category", Opportunity.Category.ACTIVE)
                        if (users) {
                            or {
                                users.each { u ->
                                    eq("userSales", u)
                                }
                            }
                        }
                        if (medias) {
                            'in'("media", medias)
                        }
                        projections {
                            groupProperty("type")
                            rowCount()
                        }
                    }
                    List enterEntry = historyDatas.find { it[0] == HistoryOpportunity.Type.ENTER_CATEGORY }
                    int enter = enterEntry ? enterEntry[1] : 0

                    List soldEntry = historyDatas.find { it[0] == HistoryOpportunity.Type.EXIT_CATEGORY_SOLD }
                    int sold = soldEntry ? soldEntry[1] : 0

                    double rate = sold ? 100 : 0
                    if (enter) {
                        rate = sold * 100 / enter
                    }
                    dataRatioCategory.add(Math.round(rate * 10) / 10)
                    dataSoldCategory.add(sold)
                    if (start > toCalendar) break
                }

                datasetCategory.put("ratio", dataRatioCategory)
                datasetCategory.put("sold", dataSoldCategory)
                datasets.add(datasetCategory)

                jsonData.put("datasets", datasets)
                jsonData.put("labels", labels)
            }
        }
        render jsonData as JSON
    }

    def getStatusAndActiveLines() {
        log.debug "getStatusAndActiveLines:" + params
        Map jsonData = [:]
        if (params.userIds && params.from && params.to) {
            boolean isMonthInterval = params.interval == "month"
            // Parse params
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)

            List<User> users = params.userIds.split(',').collect { User.get(it) }.findAll()
            List<Media> medias = params.medias ? params.medias.split(',').collect { Media.getById(Integer.valueOf(it)) } : null
            // Set base calendars
            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            // Check if dates are valid
            if (fromCalendar < toCalendar) {
                // Set the calendar at the start of week/month
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)
                // Set the values for incremetation on dates by a month/week
                int calendarIncrementProperty = isMonthInterval ? Calendar.MONTH : Calendar.DATE
                int calendarIncrementAmount = isMonthInterval ? 1 : 7

                List<String> labels = []
                // Build the time labels
                for (;;) {
                    if (isMonthInterval) {
                        DateFormat formatter = new SimpleDateFormat("MMMM yyyy", request.locale);
                        String s = formatter.format(fromCalendar.getTime())
                        labels.add(s.substring(0, 1).toUpperCase() + s.substring(1))
                    } else {
                        labels.add(fromCalendar.format(g.message(code: "dateFormat")))
                    }
                    fromCalendar.add(calendarIncrementProperty, calendarIncrementAmount)
                    if (fromCalendar > toCalendar) break
                }
                jsonData.labels = labels
                // Reset the from calendar
                fromCalendar.setTime(from)
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)

                // Initialise the map that will parse the data to collect sold/exit/enter/ratio lists
                Map datasetsMap = Opportunity.Category.ACTIVE.status.collectEntries  { [
                        (it.id): [
                                sold: [],
                                exit: [],
                                enter: [],
                                ratio: [],
                                // Contains values for live calculs
                                (HistoryOpportunity.Type.ENTER_STATUS.id):0,
                                (HistoryOpportunity.Type.EXIT_STATUS.id):0,
                                (HistoryOpportunity.Type.EXIT_STATUS_SOLD.id):0
                        ]
                ]}

                // Loop each week/month
                Calendar start = fromCalendar.clone()
                for (;;) {
                    // Query
                    Date STARTMONTH = start.getTime()
                    start.add(calendarIncrementProperty, calendarIncrementAmount)
                    Date ENDMONTH = start.getTime()
                    List historyDatas = HistoryOpportunity.createCriteria().list {
                        gt("date", STARTMONTH)
                        lt("date", ENDMONTH)
                        gt("date", from)
                        lt("date", to)
                        or {
                            Opportunity.Category.ACTIVE.status.each { s ->
                                eq("status", s)
                            }
                        }
                        or {
                            eq("type", HistoryOpportunity.Type.ENTER_STATUS)
                            eq("type", HistoryOpportunity.Type.EXIT_STATUS_SOLD)
                            eq("type", HistoryOpportunity.Type.EXIT_STATUS)
                        }
                        if (users) {
                            or {
                                users.each { u ->
                                    eq("userSales", u)
                                }
                            }
                        }
                        if (medias) {
                            'in'("media", medias)
                        }
                        projections {
                            groupProperty("status")
                            groupProperty("type")
                            rowCount()
                        }
                    }
                    // Reset the map with 0 values
                    datasetsMap.each {
                        it.value[HistoryOpportunity.Type.ENTER_STATUS.id] = 0
                        it.value[HistoryOpportunity.Type.EXIT_STATUS.id] = 0
                        it.value[HistoryOpportunity.Type.EXIT_STATUS_SOLD.id] = 0
                    }
                    // Fill the map with the query results
                    historyDatas.each {
                        datasetsMap[it[0].id][it[1].id] = it[2]
                    }
                    // add the data to each list
                    datasetsMap.each {
                        int exit = it.value[HistoryOpportunity.Type.EXIT_STATUS.id]
                        int sold = it.value[HistoryOpportunity.Type.EXIT_STATUS_SOLD.id]
                        int enter = it.value[HistoryOpportunity.Type.ENTER_STATUS.id]
                        int ratio = enter ? sold * 100 / enter : sold ? 100 : 0
                        it.value.sold << sold
                        it.value.enter << enter
                        it.value.exit << exit
                        it.value.ratio << ratio
                    }
                    if (start > toCalendar) break
                }
                // Loop end

                // Map for active category
                Map datasetsActive = [
                        sold: [],
                        exit: [],
                        enter: [],
                        ratio: [],
                        // Contains values for live calculs
                        (HistoryOpportunity.Type.ENTER_STATUS.id):0,
                        (HistoryOpportunity.Type.EXIT_STATUS.id):0,
                        (HistoryOpportunity.Type.EXIT_STATUS_SOLD.id):0
                ]

                // Loop
                start = fromCalendar.clone()
                for (;;) {
                    // Query
                    Date STARTMONTH = start.getTime()
                    start.add(calendarIncrementProperty, calendarIncrementAmount)
                    Date ENDMONTH = start.getTime()
                    List historyDatas = HistoryOpportunity.createCriteria().list {
                        gt("date", STARTMONTH)
                        lt("date", ENDMONTH)
                        gt("date", from)
                        lt("date", to)
                        eq("category", Opportunity.Category.ACTIVE)
                        or {
                            eq("type", HistoryOpportunity.Type.ENTER_CATEGORY)
                            eq("type", HistoryOpportunity.Type.EXIT_CATEGORY_SOLD)
                            eq("type", HistoryOpportunity.Type.EXIT_CATEGORY)
                        }
                        if (users) {
                            or {
                                users.each { u ->
                                    eq("userSales", u)
                                }
                            }
                        }
                        if (medias) {
                            'in'("media", medias)
                        }
                        projections {
                            groupProperty("type")
                            rowCount()
                        }
                    }
                    // Reset the values
                    datasetsActive[HistoryOpportunity.Type.ENTER_CATEGORY.id] = 0
                    datasetsActive[HistoryOpportunity.Type.EXIT_CATEGORY.id] = 0
                    datasetsActive[HistoryOpportunity.Type.EXIT_CATEGORY_SOLD.id] = 0
                    // Fill values with query results
                    historyDatas.each {
                        datasetsActive[it[0].id] = it[1]
                    }
                    // Process the result to fill lists
                    int exit = datasetsActive[HistoryOpportunity.Type.EXIT_CATEGORY.id]
                    int sold = datasetsActive[HistoryOpportunity.Type.EXIT_CATEGORY_SOLD.id]
                    int enter = datasetsActive[HistoryOpportunity.Type.ENTER_CATEGORY.id]
                    int ratio = enter ? sold * 100 / enter : sold ? 100 : 0
                    datasetsActive.sold << sold
                    datasetsActive.enter << enter
                    datasetsActive.exit << exit
                    datasetsActive.ratio << ratio
                    if (start > toCalendar) break
                }
                // Loop end for category
                jsonData.datasetsActive = datasetsActive
                jsonData.datasetsMap = datasetsMap
            }
        }
        render jsonData as JSON
    }

    def getBarChart() {
        log.debug "getBarChart:" + params
        Map jsonData = [:]
        if (params.userIds && params.from && params.to) {
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)

            List<User> users = params.userIds.split(',').collect { User.get(it) }.findAll()
            List<Media> medias = params.medias ? params.medias.split(',').collect { Media.getById(Integer.valueOf(it)) } : null

            Opportunity.Status status
            if (params.statusId) {
                status = Opportunity.Status.getById(Integer.valueOf(params.statusId))
            }

            List<String> labels = []
            users.each {
                labels.add(it.username)
            }
            jsonData.labels = labels

            // Initialise the map that will parse the data to collect sold/exit/enter/ratio lists
            Map datasetsMap = users.collectEntries { u -> [
                    (u.id): [
                            sold: 0,
                            exit: 0,
                            enter: 0
                    ]
            ]}

            HistoryOpportunity.Type historyTypeEnter = status ? HistoryOpportunity.Type.ENTER_STATUS : HistoryOpportunity.Type.ENTER_CATEGORY
            HistoryOpportunity.Type historyTypeSold = status ? HistoryOpportunity.Type.EXIT_STATUS_SOLD : HistoryOpportunity.Type.EXIT_CATEGORY_SOLD
            HistoryOpportunity.Type historyTypeExit = status ? HistoryOpportunity.Type.EXIT_STATUS : HistoryOpportunity.Type.EXIT_CATEGORY

            List historyDatas = HistoryOpportunity.createCriteria().list {
                gt("date", from)
                lt("date", to)
                or {
                    eq("type", historyTypeEnter)
                    eq("type", historyTypeSold)
                    eq("type", historyTypeExit)
                }
                if (status) {
                    eq("status", status)
                }
                else {
                    eq("category", Opportunity.Category.ACTIVE)
                }
                isNotNull("userSales")
                or {
                    users.each { u ->
                        eq("userSales", u)
                    }
                }
                if (medias) {
                    'in'("media", medias)
                }
                projections {
                    groupProperty("userSales")
                    groupProperty("type")
                    rowCount()
                }
            }
            historyDatas.each {
                switch (it[1].id) {
                    case historyTypeSold.id:
                        datasetsMap[it[0].id].sold = it[2]
                        break
                    case historyTypeEnter.id:
                        datasetsMap[it[0].id].enter = it[2]
                        break
                    case historyTypeExit.id:
                        datasetsMap[it[0].id].exit = it[2]
                        break
                }
            }
            List sold = []
            List enter = []
            List exit = []
            List ratio = []
            datasetsMap.each {
                sold.add(it.value.sold)
                enter.add(it.value.enter)
                exit.add(it.value.exit)
                ratio.add(it.value.enter ? Math.round((it.value.sold * 1000 / it.value.enter) / 10) : (it.value.sold ? 100 : 0))
            }
            jsonData.sold = sold
            jsonData.enter = enter
            jsonData.exit = exit
            jsonData.ratio = ratio
        }
        render jsonData as JSON
    }

}