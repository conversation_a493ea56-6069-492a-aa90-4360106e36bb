package traction

import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.communication.CallData
import traction.department.DepartmentGroup
import traction.goal.DailyUserData
import traction.history.History
import traction.security.User

import java.text.DateFormat
import java.text.SimpleDateFormat

//@Secured('ROLE_USER')
@Secured("@securityService.secured('PERM_POINT_REPORT')")
class PointReportController {

    def springSecurityService
    def historyService
    def userPointService
    def callReportService
    def userGoalService
    def salesDashboardService
    def potentialOpportunityService
    def opportunityService
    def taskService

    def index() {
        List<User> users = [springSecurityService.currentUser]
        Date to = new Date()
        Date from = to - 90
        String fromToInput = "${from.format("MM/dd/yyyy")} - ${to.format("MM/dd/yyyy")}"
        List<DepartmentGroup> departmentGroups = DepartmentGroup.createCriteria().list {
            'in'("userGroup", [
                    UserGroup.SALES,
                    UserGroup.PARTS,
                    UserGroup.SERVICE,
                    UserGroup.FNI
            ])
        }
        render(view: "index", model: [users: users, departmentGroups: departmentGroups, fromToInput: fromToInput])
    }

    def statsUsers() {
        log.debug "statsUsers:" + params
        List<User> users = []
        if (params.userIds) {
            params.userIds.split(',').each {
                User user = User.get(it)
                if (user) {
                    users.add(user)
                }
            }
        }
        else {
            users.add(springSecurityService.currentUser)
        }
        Date from = Date.parse("yyyy-MM-dd", params.from)
        Date to = Date.parse("yyyy-MM-dd", params.to) + 1
        String interval = params.interval

        List leaders = historyService.getPointsLeaders(users)

        List histories = History.createCriteria().list {
            ge("date", from)
            lt("date", to)
            or {
                users.each { u ->
                    eq("user", u)
                }
            }
            projections {
                groupProperty("status")
                rowCount()
                sum("points")
            }
        }
        List<History.Status> statusList = [
                History.Status.OPPORTUNITYSOLD,
                History.Status.INBOUNDCALL,
                History.Status.OUTBOUNDCALL,
                History.Status.SMS_SENT,
                History.Status.EMAILSENT,
                History.Status.APPOINTMENT,
                History.Status.WALK_IN,
                History.Status.TASK_CREATED
        ]

        int pointsToday = historyService.getPointsToday(users)
        int targetToday = userPointService.getTodayTarget(users)
        double ratioDay = targetToday ? (pointsToday * 1.0 / targetToday) : 0
        String pointsTodayColor = userGoalService.getGoalRankingColor(ratioDay)


        int pointsMonth = historyService.getPointsMonth(users)
        int targetMonth = userPointService.getMonthTarget(users)
        double ratioMonth = targetMonth ? (pointsMonth * 1.0 / targetMonth) : 0
        String pointsMonthColor = userGoalService.getGoalRankingColor(ratioMonth)

        int takeoverToday = historyService.getTodayTakeOverCount(users)
        int rdv7days = salesDashboardService.getAppointment7Days(users)
        int soldToday = salesDashboardService.getTodaySoldCount(users)
        int soldMonth = salesDashboardService.getMonthSoldCount(users)
        int soldRefusedToday = salesDashboardService.getTodayLostSales(users)
        int soldLastYearDay = salesDashboardService.getLastYearDaySoldCount(users)
        int soldLastYearMonth = salesDashboardService.getLastYearMonthSoldCount(users)

        List calls = CallData.Category.values().collect {
            [
                    category: it,
                    count   : callReportService.getTodayUsersCallCount(users, it)
            ]
        }
        int missedCalls = callReportService.getTodayAllUsersMissedCount(users)
        int potentialOpportunityToday = potentialOpportunityService.countToday(users)

        int todo = taskService.getTodoCount(users) + callReportService.getUsersNotesTodoCount(users)
        int awaiting = opportunityService.getWaitingCount(users)

        Date dateNoMail= new Date() + 1
        dateNoMail.clearTime()
        int noMailClient = opportunityService.getNoEmailClientOpportunitiesCount(users, dateNoMail - 1, dateNoMail)

        String goalReached = "background-color: rgba(0, 170, 58, 0.68);"
        String goalNotReached = "background-color: rgba(255, 39, 39, 0.68);"
        String lastYearReached = "background-color: rgba(255, 220, 0, 0.68);"
        String colorSoldDay
        String colorSoldMonth

        int dayGoal = userGoalService.getUsersDaySalesGoal(users)
        int monthGoal = userGoalService.getUsersMonthSalesGoal(users)

        if (dayGoal <= soldToday) {
            colorSoldDay = goalReached
        } else if (soldToday >= soldLastYearDay) {
            colorSoldDay = lastYearReached
        } else {
            colorSoldDay = goalNotReached
        }

        if (monthGoal <= soldMonth) {
            colorSoldMonth = goalReached
        } else if (soldMonth >= soldLastYearMonth) {
            colorSoldMonth = lastYearReached
        }  else {
            colorSoldMonth = goalNotReached
        }

        render(template: "statsUsers", model: [
                stacked: params.stacked == "true",
                users: users,
                from: from,
                to: to,
                interval: interval,
                leaders: leaders,
                histories: histories,
                statusList: statusList,
                pointsToday: pointsToday,
                targetToday: targetToday,
                ratioDay: ratioDay,
                pointsTodayColor: pointsTodayColor,
                pointsMonth: pointsMonth,
                targetMonth: targetMonth,
                ratioMonth: ratioMonth,
                pointsMonthColor: pointsMonthColor,
                takeoverToday: takeoverToday,
                rdv7days: rdv7days,
                soldToday: soldToday,
                soldMonth: soldMonth,
                soldRefusedToday: soldRefusedToday,
                soldLastYearDay: soldLastYearDay,
                soldLastYearMonth: soldLastYearMonth,
                calls: calls,
                missedCalls: missedCalls,
                potentialOpportunityToday: potentialOpportunityToday,
                awaiting: awaiting,
                todo: todo,
                monthGoal: monthGoal,
                dayGoal: dayGoal,
                colorSoldMonth: colorSoldMonth,
                colorSoldDay: colorSoldDay,
                noMailClient: noMailClient,

        ])
    }

    def getAreaChartData() {
        JSONObject jsonObject = new JSONObject()
        if (params.userIds && params.from && params.to) {
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)

            boolean negative = params.negative == "true"

            List histories = History.createCriteria().list {
                ge("date", from)
                lt("date", to)
                or {
                    params.userIds.split(',').each {
                        eq("user.id", Long.valueOf(it))
                    }
                }
                if (negative) {
                    lt("points", 0D)
                }
                else {
                    gt("points", 0D)
                }
                projections {
                    groupProperty("status")
                    sum("points")
                }
            }

            JSONArray datasets = new JSONArray()
            JSONArray labels = new JSONArray()

            JSONObject dataset = new JSONObject()
            dataset.put("label", "MA LEGEND")

            JSONArray data = new JSONArray()
            JSONArray backgroundColor = new JSONArray()

            histories.each {
                labels.add(g.message(code: it[0].message))
                backgroundColor.add(it[0].iconColor)
                data.add(Math.abs(it[1]))
            }

            dataset.put("backgroundColor", backgroundColor)
            dataset.put("data", data)
            datasets.add(dataset)
            jsonObject.put("datasets", datasets)
            jsonObject.put("labels", labels)
        }
        render jsonObject as JSON
    }

    def getSalesObjectiveChart() {
        JSONObject jsonData = new JSONObject()
        if (params.userIds && params.from && params.to) {
            boolean isMonthInterval = params.interval == "month"
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)
            List<User> users = []
            params.userIds.split(',').each {
                User user = User.get(it)
                if (user) {
                    users.add(user)
                }
            }
            JSONArray labels = new JSONArray()
            JSONArray datasets = new JSONArray()
            // Get this month start
            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            if (fromCalendar < toCalendar) {
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)
                int calendarIncrementProperty = isMonthInterval ? Calendar.MONTH : Calendar.DATE
                int calendarIncrementAmount = isMonthInterval ? 1 : 7
                for (;;) {
                    if (isMonthInterval) {
                        DateFormat formatter = new SimpleDateFormat("MMMM yyyy", request.locale);
                        String s = formatter.format(fromCalendar.getTime())
                        labels.add(s.substring(0, 1).toUpperCase() + s.substring(1))
                    } else {
                        labels.add(fromCalendar.format(g.message(code: "dateFormat")))
                    }
                    fromCalendar.add(calendarIncrementProperty, calendarIncrementAmount)
                    if (fromCalendar > toCalendar) break
                }
                fromCalendar.setTime(from)
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)

                if (users) {
                    List totalData = History.createCriteria().list {
                        ge("date", from)
                        lt("date", to)
                        eq("status", History.Status.OPPORTUNITYSOLD)
                        or {
                            if (users.size() == 1) {
                                users.first().getDepartmentGroups().each { d ->
                                    d.users.each { u ->
                                        eq("user", u)
                                    }
                                }
                            }
                            else {
                                users.each { u ->
                                    eq("user", u)
                                }
                            }
                        }
                        projections {
                            groupProperty("user")
                            rowCount()
                        }
                    }
                    User bestUser = totalData ? totalData.sort { it[1] }.last()[0] : null

                    JSONObject dataset = new JSONObject()
                    dataset.put("label", g.message(code: "total.sales"))
                    dataset.put("backgroundColor", "#ff0000aa")
                    dataset.put("borderColor", "red")
                    dataset.put("fill", true)
                    dataset.put("tension", 0.4)
                    JSONArray data = new JSONArray()

                    JSONObject datasetGoals = new JSONObject()
                    datasetGoals.put("label", g.message(code: "total.objectives"))
                    datasetGoals.put("backgroundColor", "green")
                    datasetGoals.put("borderColor", "green")
                    datasetGoals.put("fill", false)
                    datasetGoals.put("tension", 0.4)
                    JSONArray borderDash = new JSONArray()
                    borderDash.add(5)
                    borderDash.add(5)
                    datasetGoals.put("borderDash", borderDash)
                    JSONArray dataGoals = new JSONArray()

                    JSONObject datasetBest = new JSONObject()
                    datasetBest.put("label", bestUser ? "${bestUser.fullName} (${g.message(code: "best")})" : "---")
                    datasetBest.put("backgroundColor", bestUser?.color)
                    datasetBest.put("borderColor", bestUser?.color)
                    datasetBest.put("fill", false)
                    datasetBest.put("tension", 0.4)
                    JSONArray dataBest = new JSONArray()

                    Calendar start = fromCalendar.clone()
                    for (;;) {
                        Date STARTMONTH = start.getTime()
                        start.add(calendarIncrementProperty, calendarIncrementAmount)
                        Date ENDMONTH = start.getTime()
                        int countUser = History.createCriteria().count() {
                            ge("date", STARTMONTH)
                            lt("date", ENDMONTH)
                            ge("date", from)
                            lt("date", to)
                            or {
                                users.each { user ->
                                    eq("user", user)
                                }
                            }
                            eq("status", History.Status.OPPORTUNITYSOLD)
                        }
                        data.add(countUser)

                        int countBestUser = 0
                        if (bestUser) {
                            countBestUser = History.createCriteria().count() {
                                ge("date", STARTMONTH)
                                lt("date", ENDMONTH)
                                ge("date", from)
                                lt("date", to)
                                eq("user", bestUser)
                                eq("status", History.Status.OPPORTUNITYSOLD)
                            }
                        }
                        dataBest.add(countBestUser)

                        Double goal = DailyUserData.createCriteria().get {
                            ge("date", STARTMONTH)
                            lt("date", ENDMONTH)
                            ge("date", from)
                            lt("date", to)
                            or {
                                users.each { u ->
                                    eq("user", u)
                                }
                            }
                            projections {
                                sum("monthSalesTarget")
                            }
                        }
                        dataGoals.add(goal ? Math.round(goal) : 0)
                        if (start > toCalendar) break
                    }

                    dataset.put("data", data)
                    datasetBest.put("data", dataBest)
                    datasetGoals.put("data", dataGoals)
                    datasets.add(datasetBest)
                    datasets.add(datasetGoals)
                    datasets.add(dataset)
                }

                // Set json values
                jsonData.put("labels", labels)
                jsonData.put("datasets", datasets)
            }
            else {
                log.debug "Invalid dates"
            }
        }
        render jsonData as JSON
    }

    def getPointsObjectiveChart() {
        JSONObject jsonData = new JSONObject()
        if (params.userIds && params.from && params.to) {
            boolean isMonthInterval = params.interval == "month"
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)
            List<User> users = []
            params.userIds.split(',').each {
                User user = User.get(it)
                if (user) {
                    users.add(user)
                }
            }
            JSONArray labels = new JSONArray()
            JSONArray datasets = new JSONArray()
            // Get this month start
            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            if (fromCalendar < toCalendar) {
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)
                int calendarIncrementProperty = isMonthInterval ? Calendar.MONTH : Calendar.DATE
                int calendarIncrementAmount = isMonthInterval ? 1 : 7
                for (;;) {
                    if (isMonthInterval) {
                        DateFormat formatter = new SimpleDateFormat("MMMM yyyy", request.locale);
                        String s = formatter.format(fromCalendar.getTime())
                        labels.add(s.substring(0, 1).toUpperCase() + s.substring(1))
                    } else {
                        labels.add(fromCalendar.format(g.message(code: "dateFormat")))
                    }
                    fromCalendar.add(calendarIncrementProperty, calendarIncrementAmount)
                    if (fromCalendar > toCalendar) break
                }
                fromCalendar.setTime(from)
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)

                if (users) {
                    List totalData = History.createCriteria().list {
                        ge("date", from)
                        lt("date", to)
                        or {
                            if (users.size() == 1) {
                                users.first().getDepartmentGroups().each { d ->
                                    d.users.each { u ->
                                        eq("user", u)
                                    }
                                }
                            }
                            else {
                                users.each { u ->
                                    eq("user", u)
                                }
                            }
                        }
                        projections {
                            groupProperty("user")
                            sum("points")
                        }
                    }
                    User bestUser = totalData ? totalData.sort { it[1] }.last()[0] : null

                    JSONObject dataset = new JSONObject()
                    dataset.put("label", g.message(code: "history.points.total"))
                    dataset.put("backgroundColor", "#ff0000aa")
                    dataset.put("borderColor", "red")
                    dataset.put("fill", true)
                    dataset.put("tension", 0.4)
                    JSONArray data = new JSONArray()

                    JSONObject datasetGoals = new JSONObject()
                    datasetGoals.put("label", g.message(code: "total.objectives"))
                    datasetGoals.put("backgroundColor", "green")
                    datasetGoals.put("borderColor", "green")
                    datasetGoals.put("fill", false)
                    datasetGoals.put("tension", 0.4)
                    JSONArray borderDash = new JSONArray()
                    borderDash.add(5)
                    borderDash.add(5)
                    datasetGoals.put("borderDash", borderDash)
                    JSONArray dataGoals = new JSONArray()

                    JSONObject datasetBest = new JSONObject()
                    datasetBest.put("label", bestUser ? "${bestUser.fullName} (${g.message(code: "best")})" : "---")
                    datasetBest.put("backgroundColor", bestUser?.color)
                    datasetBest.put("borderColor", bestUser?.color)
                    datasetBest.put("fill", false)
                    datasetBest.put("tension", 0.4)

                    JSONArray dataBest = new JSONArray()

                    Calendar start = fromCalendar.clone()
                    for (;;) {
                        Date STARTMONTH = start.getTime()
                        start.add(calendarIncrementProperty, calendarIncrementAmount)
                        Date ENDMONTH = start.getTime()

                        Double countUsers = History.createCriteria().get {
                            ge("date", STARTMONTH)
                            lt("date", ENDMONTH)
                            ge("date", from)
                            lt("date", to)
                            or {
                                users.each { u ->
                                    eq("user", u)
                                }
                            }
                            projections {
                                sum("points")
                            }
                        }
                        data.add(countUsers ?: 0)

                        Integer countBestUser = 0
                        if (bestUser) {
                            countBestUser = History.createCriteria().get {
                                ge("date", STARTMONTH)
                                lt("date", ENDMONTH)
                                ge("date", from)
                                lt("date", to)
                                eq("user", bestUser)
                                projections {
                                    sum("points")
                                }
                            }
                        }
                        dataBest.add(countBestUser ?: 0)

                        Double goal = DailyUserData.createCriteria().get {
                            ge("date", STARTMONTH)
                            lt("date", ENDMONTH)
                            ge("date", from)
                            lt("date", to)
                            or {
                                users.each { u ->
                                    eq("user", u)
                                }
                            }
                            projections {
                                sum("monthPointsTarget")
                            }
                        }
                        dataGoals.add(goal ? Math.round(goal) : 0)
                        if (start > toCalendar) break
                    }

                    dataset.put("data", data)
                    datasetBest.put("data", dataBest)
                    datasetGoals.put("data", dataGoals)
                    datasets.add(datasetBest)
                    datasets.add(datasetGoals)
                    datasets.add(dataset)
                }

                // Set json values
                jsonData.put("labels", labels)
                jsonData.put("datasets", datasets)
            }
            else {
                log.debug "Invalid dates"
            }
        }
        render jsonData as JSON
    }

    def userHistoryChart() {
        JSONObject jsonData = new JSONObject()
        if (params.userIds && (params.statusId || params.dailyUserDataProperty)) {
            boolean stacked = params.stacked == "true"
            boolean isMonthInterval = params.interval == "month"
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)
            List<User> users = []
            params.userIds.split(',').each {
                User user = User.get(it)
                if (user) {
                    users.add(user)
                }
            }
            JSONArray labels = new JSONArray()
            JSONArray datasets = new JSONArray()
            // Get this month start
            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            if (fromCalendar < toCalendar) {
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)
                int calendarIncrementProperty = isMonthInterval ? Calendar.MONTH : Calendar.DATE
                int calendarIncrementAmount = isMonthInterval ? 1 : 7
                for (;;) {
                    if (isMonthInterval) {
                        DateFormat formatter = new SimpleDateFormat("MMMM yyyy", request.locale);
                        String s = formatter.format(fromCalendar.getTime())
                        labels.add(s.substring(0, 1).toUpperCase() + s.substring(1))
                    } else {
                        labels.add(fromCalendar.format(g.message(code: "dateFormat")))
                    }
                    fromCalendar.add(calendarIncrementProperty, calendarIncrementAmount)
                    if (fromCalendar > toCalendar) break
                }
                fromCalendar.setTime(from)
                fromCalendar.set(isMonthInterval ? Calendar.DAY_OF_MONTH : Calendar.DAY_OF_WEEK, 1)

                if (users) {
                    if (params.dailyUserDataProperty) { // "scheduledTask" or "lateTask"
                        String property = params.dailyUserDataProperty

                        List totalData = DailyUserData.createCriteria().list {
                            ge("date", from)
                            lt("date", to)
                            or {
                                if (users.size() == 1) {
                                    users.first().getDepartmentGroups().each { d ->
                                        d.users.each { u ->
                                            eq("user", u)
                                        }
                                    }
                                }
                                else {
                                    users.each { u ->
                                        eq("user", u)
                                    }
                                }
                            }
                            projections {
                                groupProperty("user")
                                sum(property)
                            }
                        }
                        totalData = totalData.sort { it[1] }
                        User bestUser
                        if (totalData) {
                            bestUser = (property == "lateTask") ? totalData.first()[0] : totalData.last()[0]
                        }
                        if (users.size() == 1) {
                            User user = users.first()
                            JSONObject dataset = new JSONObject()
                            dataset.put("label", user.fullName)
                            dataset.put("backgroundColor", user.color)
                            dataset.put("borderColor", user.color)
                            dataset.put("fill", stacked)
                            dataset.put("tension", 0.4)
                            JSONArray data = new JSONArray()

                            JSONObject datasetBest = new JSONObject()
                            datasetBest.put("label", bestUser ? bestUser.fullName : "---")
                            datasetBest.put("backgroundColor", bestUser?.color)
                            datasetBest.put("borderColor", bestUser?.color)
                            datasetBest.put("fill", stacked)
                            datasetBest.put("tension", 0.4)
                            JSONArray dataBest = new JSONArray()

                            Calendar start = fromCalendar.clone()
                            for (;;) {
                                Date STARTMONTH = start.getTime()
                                start.add(calendarIncrementProperty, calendarIncrementAmount)
                                Date ENDMONTH = start.getTime()
                                Integer countUser = DailyUserData.createCriteria().get {
                                    ge("date", STARTMONTH)
                                    lt("date", ENDMONTH)
                                    ge("date", from)
                                    lt("date", to)
                                    eq("user", user)
                                    projections {
                                        avg(property)
                                    }
                                }
                                data.add(countUser ?: 0)

                                Integer countBestUser = 0
                                if (bestUser) {
                                    countBestUser = DailyUserData.createCriteria().get {
                                        ge("date", STARTMONTH)
                                        lt("date", ENDMONTH)
                                        ge("date", from)
                                        lt("date", to)
                                        eq("user", bestUser)
                                        projections {
                                            avg(property)
                                        }
                                    }
                                }
                                dataBest.add(countBestUser  ?: 0)
                                if (start > toCalendar) break
                            }

                            dataset.put("data", data)
                            datasetBest.put("data", dataBest)
                            datasets.add(dataset)
                            datasets.add(datasetBest)
                        } else { // More than one user
                            users.each { user ->
                                boolean isBest = (user == bestUser)
                                JSONObject dataset = new JSONObject()
                                dataset.put("label", user.fullName + (isBest ? " (${g.message(code: "best")})" : ""))
                                dataset.put("backgroundColor", user.color)
                                dataset.put("borderColor", user.color)
                                dataset.put("fill", stacked)
                                dataset.put("tension", 0.4)
                                JSONArray data = new JSONArray()
                                Calendar start = fromCalendar.clone()
                                for (;;) {
                                    Date STARTMONTH = start.getTime()
                                    start.add(calendarIncrementProperty, calendarIncrementAmount)
                                    Date ENDMONTH = start.getTime()
                                    Integer countUser = DailyUserData.createCriteria().get() {
                                        ge("date", STARTMONTH)
                                        lt("date", ENDMONTH)
                                        ge("date", from)
                                        lt("date", to)
                                        eq("user", user)
                                        projections {
                                            avg(property)
                                        }
                                    }
                                    data.add(countUser ?: 0)
                                    if (start > toCalendar) break
                                }
                                dataset.put("data", data)
                                datasets.add(dataset)
                            }
                        }

                    } else {
                        History.Status status = History.Status.getById(Integer.valueOf(params.statusId))
                        if (status) {
                            List totalData = History.createCriteria().list {
                                ge("date", from)
                                lt("date", to)
                                eq("status", status)
                                isNotNull("user")
                                or {
                                    if (users.size() == 1) {
                                        users.first().getDepartmentGroups().each { d ->
                                            d.users.each { u ->
                                                eq("user", u)
                                            }
                                        }
                                    }
                                    else {
                                        users.each { u ->
                                            eq("user", u)
                                        }
                                    }
                                }
                                projections {
                                    groupProperty("user")
                                    rowCount()
                                }
                            }
                            User bestUser = totalData ? totalData.sort { it[1] }.last()[0] : null

                            if (users.size() == 1) {
                                User user = users.first()
                                JSONObject dataset = new JSONObject()
                                dataset.put("label", user.fullName)
                                dataset.put("backgroundColor", user.color)
                                dataset.put("borderColor", user.color)
                                dataset.put("fill", stacked)
                                dataset.put("tension", 0.4)
                                JSONArray data = new JSONArray()

                                JSONObject datasetBest = new JSONObject()
                                datasetBest.put("label", bestUser ? bestUser.fullName : "---")
                                datasetBest.put("backgroundColor", bestUser?.color)
                                datasetBest.put("borderColor", bestUser?.color)
                                datasetBest.put("fill", stacked)
                                datasetBest.put("tension", 0.4)
                                JSONArray dataBest = new JSONArray()

                                Calendar start = fromCalendar.clone()
                                for (;;) {
                                    Date STARTMONTH = start.getTime()
                                    start.add(calendarIncrementProperty, calendarIncrementAmount)
                                    Date ENDMONTH = start.getTime()
                                    int countUser = History.createCriteria().count() {
                                        ge("date", STARTMONTH)
                                        lt("date", ENDMONTH)
                                        ge("date", from)
                                        lt("date", to)
                                        eq("user", user)
                                        eq("status", status)
                                    }
                                    data.add(countUser)

                                    int countBestUser = 0
                                    if (bestUser) {
                                        countBestUser = History.createCriteria().count() {
                                            ge("date", STARTMONTH)
                                            lt("date", ENDMONTH)
                                            ge("date", from)
                                            lt("date", to)
                                            eq("user", bestUser)
                                            eq("status", status)
                                        }
                                    }
                                    dataBest.add(countBestUser)
                                    if (start > toCalendar) break
                                }

                                dataset.put("data", data)
                                datasetBest.put("data", dataBest)
                                datasets.add(dataset)
                                datasets.add(datasetBest)
                            } else { // More than one user
                                users.each { user ->
                                    boolean isBest = (user == bestUser)
                                    JSONObject dataset = new JSONObject()
                                    dataset.put("label", user.fullName + (isBest ? " (BEST)" : ""))
                                    dataset.put("backgroundColor", user.color)
                                    dataset.put("borderColor", user.color)
                                    dataset.put("fill", stacked)
                                    JSONArray data = new JSONArray()

                                    Calendar start = fromCalendar.clone()
                                    for (;;) {
                                        Date STARTMONTH = start.getTime()
                                        start.add(calendarIncrementProperty, calendarIncrementAmount)
                                        Date ENDMONTH = start.getTime()
                                        int countUser = History.createCriteria().count() {
                                            ge("date", STARTMONTH)
                                            lt("date", ENDMONTH)
                                            ge("date", from)
                                            lt("date", to)
                                            eq("user", user)
                                            eq("status", status)
                                        }
                                        data.add(countUser)
                                        if (start > toCalendar) break
                                    }

                                    dataset.put("data", data)
                                    datasets.add(dataset)
                                }
                            }
                        }
                    }
                }

                // Set json values
                jsonData.put("labels", labels)
                jsonData.put("datasets", datasets)
            }
            else {
                log.debug "Invalid dates"
            }
        }
        render jsonData as JSON
    }


    def getBarChart() {
        log.debug "getBarChart:" + params
        JSONObject jsonData = new JSONObject()
        if (params.userIds && params.from && params.to) {
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)

            boolean negative = params.negative == "true"

            List<User> users = []
            params.userIds.split(',').each {
                User user = User.get(it)
                if (user) {
                    users.add(user)
                }
            }
            User singleUser
            if (users.size() == 1) {
                singleUser = users.first()
                singleUser.getDepartmentGroups().each { dg ->
                    dg.users.each { u ->
                        if (u.enabled && u.visibleInSoldboard && !u.deleted && !users.contains(u)) {
                            users.add(u)
                        }
                    }
                }
            }

            JSONArray labels = new JSONArray()
            JSONArray datasets = new JSONArray()

            JSONArray datasetBgColors = new JSONArray()
            JSONArray datasetColors = new JSONArray()
            users.each {
                labels.add(it.username)
                datasetBgColors.add(it.color + "88")
                datasetColors.add(it.color)
            }

            JSONObject dataset = new JSONObject()
            dataset.put("label", g.message(code: "history.points"))
            dataset.put("backgroundColor", datasetBgColors)
            dataset.put("borderColor", datasetColors)
            dataset.put("borderWidth", 1)

            List historyDatas = History.createCriteria().list {
                gt("date", from)
                lt("date", to)
                or {
                    users.each { u ->
                        eq("user", u)
                    }
                }
                if (negative) {
                    lt("points", 0D)
                }
                else {
                    gt("points", 0D)
                }
                projections {
                    groupProperty("user")
                    sum("points")
                }
            }

            JSONArray datasetArray = new JSONArray()
            users.each { user ->
                List entry = historyDatas.find { it[0] == user }
                datasetArray.add(entry ? Math.abs(entry[1]) : 0)
            }
            dataset.put("data", datasetArray)

            datasets.add(dataset)

            jsonData.put("datasets", datasets)
            jsonData.put("labels", labels)
        }
        render jsonData as JSON
    }
}
