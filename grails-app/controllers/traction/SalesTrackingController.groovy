package traction

import grails.converters.JSON
import grails.gorm.PagedResultList
import grails.plugin.springsecurity.annotation.Secured
import traction.client.ClientInterest
import traction.datatable.DataTableColumnFilters
import traction.cotation.CotationElement
import traction.cotation.CotationElementSearch
import traction.department.Department
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityLabel
import traction.render.RenderUtils
import traction.permissions.Permission
import traction.salesTracking.dataTable.SalesTrackingDataTable
import traction.vehicle.Vehicle
import traction.workflow.WorkflowData
import traction.workflow.WorkflowMaster
import traction.ConfigService

//@Secured('ROLE_USER')
@Secured("@securityService.secured('PERM_SALES_TRACKING_READ')")
class SalesTrackingController {

    ConfigService configService
    def opportunityService
    def cotationService
    def securityService
    def vehicleSelectionOptionService

    def index() {
        log.debug "index ${params}"
        List<Department> departments = Department.getAll()
        List<UserGroup> userGroups = [UserGroup.SALES, UserGroup.FNI, UserGroup.BDC]

        List<CotationElement.FollowUp> followUpList = CotationElement.FollowUp.values()

        Calendar calendar = Calendar.getInstance()
        calendar.set(Calendar.DATE, 1)
        Date from = calendar.getTime()
        calendar.add(Calendar.MONTH, 1)
        Date to = calendar.getTime()
        from.clearTime()
        to.clearTime()

        List<Opportunity.Status> soldList = Opportunity.Category.SOLD.status.findAll { !it.isLostSold() }
        List<Opportunity.Status> lostList = Opportunity.Category.SOLD.status.findAll { it.isLostSold() }
        List<String> productMakes = vehicleSelectionOptionService.getMakes().collect { it.label } as List<String>
        List<ClientInterest> interests = ClientInterest.getAll()
        List<WorkflowMaster> workflowMasterList = WorkflowMaster.getAll()
        boolean salesTrackingAdminPermission = securityService.hasPermission(Permission.PERM_SALES_TRACKING_ADMIN)
        render(view: "index", model: [
                from              : from,
                to                : to,
                workflowMasterList: workflowMasterList,
                departments       : departments,
                userGroups        : userGroups,
                followUpList      : followUpList,
                soldStatus        : Opportunity.Category.SOLD.getStatus(),
                labels            : OpportunityLabel.getAll(),
                soldList          : soldList,
                lostList          : lostList,
                productMakes      : productMakes,
                interests         : interests,
                workflowMasters   : WorkflowMaster.getAll(),
                typeOfCards       : configService.getAllCardTypes(),
                salesTrackingAdminPermission : salesTrackingAdminPermission,
                // statistics: true
        ])
    }

    def dataTable() {
        log.debug "(dataTable) params: ${params}"
        render([
                uuid: params.uuid,
                html: g.render(template: "dataTable", model: [
                        id: params.id,
                        divId: params.divId,
                        // statistics: params.boolean("statistics", false),
                ])
        ] as JSON)
    }

    def getData() {
//        log.debug "(getData) params: ${params}"
//        render (new SalesTrackingDataTable(params).getData())

        CotationElementSearch search = new CotationElementSearch(new DataTableColumnFilters(params.filter), params)
        PagedResultList<CotationElement> elements = search.query()
        int countTotal = elements.totalCount
        int size = elements.size()
        log.debug "Ret: ${size}/${countTotal}"

        Map json = [
                draw           : params.draw,
                recordsTotal   : countTotal,
                recordsFiltered: countTotal,
                data           : []
        ]
        Date oneWeek = new Date() + 7
        boolean salesTrackingAdminPermission = securityService.hasPermission(Permission.PERM_SALES_TRACKING_ADMIN)
        elements.eachWithIndex { elem, index ->
            WorkflowData workflowData = elem.opportunity?.workflowData
            json.data.add([
                    id                                           : elem.id,
                    isTrade                                      : elem.trade ? true : false,
                    no                                           : search.paging.offset + index + 1,
                    dateSold                                     : elem.cotation.dateSold?.format(g.message(code: 'dateFormat')),
                    date                                         : elem.cotation.date.format(g.message(code: 'dateFormat')),
                    department                                   : elem.cotation.department?.name ?: "",
                    type                                         : g.message(code: elem.opportunity ? "opportunity" : "trade"),
                    label                                        : elem.opportunity && elem.opportunity?.labels ? [name: elem.opportunity?.labels?.first()?.name, color: elem.opportunity?.labels?.first()?.color, icon: "mdi mdi-label"] : "",
                    cotation                                     : [
                            id      : elem.cotationId,
                            name    : elem.cotation.name,
                            clientId: elem.cotation.clientId,
                    ],
                    firstname                          : elem.cotation.client.asIndividualClient()?.firstname?.capitalize(),
                    lastname                           : elem.cotation.client.asIndividualClient()?.lastname?.capitalize(),
                    userSales                          : elem.userSales?.fullName ?: "",
                    userSales2                         : elem.opportunity ? elem.opportunity?.userSales2?.fullName : "",
                    userFni                            : elem.userFni?.fullName ?: "",
                    userBdc                            : elem.opportunity ? elem.opportunity?.userBdc?.fullName : "",
                    make                               : elem.make,
                    model                              : elem.model,
                    year                               : elem.year,
                    stocknumber                        : elem.stocknumber,
                    modelcode                          : elem.modelcode,
                    serialnumber                       : elem.opportunity?.vehicle ? elem.opportunity?.vehicle?.serialNumber : null,
                    daysInStock                        : elem.opportunity ? elem.daysInStock ?: "" : null,
                    costPrice                          : salesTrackingAdminPermission ? elem.costPrice : null,
                    costPriceNoAcc                     : salesTrackingAdminPermission ? elem.costReal : null,
                    opportunityTradePriceToAbsorb      : salesTrackingAdminPermission ? elem.opportunityTradePriceToAbsorb : null,
                    sellingPrice                       : salesTrackingAdminPermission ? elem.sellingPrice : null,
                    opportunityPotentialPrice          : salesTrackingAdminPermission ? elem.opportunityPotentialPrice : null,
                    opportunityPotentialPriceLack      : salesTrackingAdminPermission && elem.opportunity ? elem.getSellingPriceNoAcc() - elem.opportunityPotentialPrice : null,
                    sellingPriceNoAcc                  : salesTrackingAdminPermission && elem.opportunity ? elem.getSellingPriceNoAcc() : null,
                    exportedToLautopak                 : [
                            exported: elem.cotation.isExportedFinal(),
                            message : elem.cotation.isExportedFinal() ? g.message(code: "cotation.exported") : ''
                    ],
                    opportunityFinalMinimumPrice       : salesTrackingAdminPermission ? elem.opportunityFinalMinimumPrice : null,
                    opportunityMrsp                    : salesTrackingAdminPermission ? elem.opportunityMrsp : null,
                    opportunityOtherFee                : salesTrackingAdminPermission ? elem.opportunityOtherFee : null,
                    opportunityIsUsed                  : elem.opportunityIsUsed == null ? "N/A" : (elem.opportunityIsUsed ? g.message(code: "yes") : g.message(code: "no")),
                    interest                           : elem.interests?.name?.join(", "),
                    opportunityPromo                   : salesTrackingAdminPermission ? [
                            promo : elem.opportunityPromo,
                            locked: elem.opportunityPromoLocked
                    ] : [],
                    // TODO: fix ca
                    opportunityPromoDate                         : salesTrackingAdminPermission ? [
                            date: null,
                            red : false
                    ] : [],
                    profit                             : salesTrackingAdminPermission ? elem.profit : null,
                    profitPercent                      : salesTrackingAdminPermission ? elem.profitPercent : null,
                    profitNoAcc                             : salesTrackingAdminPermission ? elem.profitNoAcc : null,
                    profitNoAccPercent                      : salesTrackingAdminPermission ? elem.profitNoAccPercent : null,
                    commission                         : salesTrackingAdminPermission ? elem.commission : null,
                    commissionPercent                  : salesTrackingAdminPermission ? (elem.profit ? elem.commission / elem.profit : elem.commission ? 1 : 0) : null,
                    opportunityAccessories             : salesTrackingAdminPermission ? elem.opportunityAccessories : null,
                    opportunityAccessoriesMarginPercent: salesTrackingAdminPermission ? elem.opportunityAccessoriesMarginPercent : null,
                    opportunityAccessoriesMarginSalesPricePercent: salesTrackingAdminPermission ? elem.opportunityAccessoriesMarginSalesPricePercent : null,
                    soldStatus                         : elem.cotation.soldStatus ? [
                            html     : "<i class=\"me-1 ${elem.cotation.soldStatus.icon}\"></i>" + g.message(code: elem.cotation.soldStatus.message),
                            value    : elem.cotation.soldStatus.id,
                            isFinance: elem.cotation.soldStatus.isFinance()
                    ] : null,
                    lastStatusChange                   : elem.cotation.lastStatusChange?.format('yyyy-MM-dd'),
                    comment                            : salesTrackingAdminPermission ? elem.comment : "",
                    fniProfitReserveFinance            : salesTrackingAdminPermission ? elem.fniProfitReserveFinance : null,
                    fniProfitLifeInsurance             : salesTrackingAdminPermission ? elem.fniProfitLifeInsurance : null,
                    fniProfitCriticalIllnessInsurance  : salesTrackingAdminPermission ? elem.fniProfitCriticalIllnessInsurance : null,
                    fniProfitExtendedPlan              : salesTrackingAdminPermission ? elem.fniProfitExtendedPlan : null,
                    fniProfitAestheticProtection       : salesTrackingAdminPermission ? elem.fniProfitAestheticProtection : null,
                    fniProfitOthers                    : salesTrackingAdminPermission ? elem.fniProfitOthers : null,
                    fniProfitDisabilityInsurance       : salesTrackingAdminPermission ? elem.fniProfitDisabilityInsurance : null,
                    fniProfitReplacementInsurance      : salesTrackingAdminPermission ? elem.fniProfitReplacementInsurance : null,
                    fniProfitRentalLeaseUsury          : salesTrackingAdminPermission ? elem.fniProfitRentalLeaseUsury : null,
                    fniProfitChemicals                 : salesTrackingAdminPermission ? elem.fniProfitChemicals : null,
                    fniProfitTotal                     : salesTrackingAdminPermission ? elem.fniProfitTotal : null,
                    fniProfitTotalPercent              : salesTrackingAdminPermission ? elem.fniProfitTotalPercent : null,
                    followUp                           : salesTrackingAdminPermission ? [
                            id   : elem.followUp.id,
                            color: elem.followUp.color
                    ] : [],
                    workflowData_workflowMaster                  : elem.opportunity ? workflowData ? workflowData.workflowBoard.workflowMaster.name : "" : "N/A",
                    workflowData_workflowMasterUrl               : workflowData ? g.createLink(controller: "workflow", action: "index", params: [id: workflowData.workflowBoard.workflowMasterId, opportunityId: elem.opportunityId]) : null,
                    workflowData_workflowBoard                   : elem.opportunity ? workflowData ? workflowData.workflowBoard.name : "" : "N/A",
                    workflowDataDateEnd                          : elem.opportunity ? workflowData?.dateEnd?.format(g.message(code: 'dateFormat')) ?: "" : null,
                    workflowData_workflowTypeOfCard              : (elem.opportunity ? configService.getCardType(workflowData?.typeOfCard).collect { [title: it.title, color: it.color] }[0] ?: [title: "", color: "transparent"] : [title: "N/A", color: "transparent"])
            ])
        }
        render json as JSON
    }

    @Secured("@securityService.secured('PERM_SALES_TRACKING_ADMIN')")
    def updateComment() {
        if (params.id) {
            CotationElement cotationElement = CotationElement.get(params.id)
            if (cotationElement) {
                cotationElement.comment = params.comment
                if (cotationService.save(cotationElement.cotation)) {
                    return render(RenderUtils.success("cotationelement.comment.updated"))
                }
            }
        }
        render(RenderUtils.error("cotationelement.notupdated"))
    }

    @Secured("@securityService.secured('PERM_SALES_TRACKING_ADMIN')")
    def updateCommission() {
        if (params.id && params.commission) {
            CotationElement cotationElement = CotationElement.get(params.id)
            if (cotationElement) {
                ParamsUtils.setDouble(cotationElement, "commission", params.commission)
                if (cotationService.save(cotationElement.cotation)) {
                    return render(RenderUtils.success("cotationelement.commission.updated"))
                }
            }
        }
        render(RenderUtils.error("cotationelement.notupdated"))
    }

    @Secured("@securityService.secured('PERM_SALES_TRACKING_ADMIN')")
    def updateFollowUp() {
        if (params.id && params.value) {
            CotationElement cotationElement = CotationElement.get(params.id)
            CotationElement.FollowUp followUp = CotationElement.FollowUp.getById(Integer.valueOf(params.value))
            if (cotationElement && followUp) {
                cotationElement.followUp = followUp
                if (cotationService.save(cotationElement.cotation)) {
                    return render(RenderUtils.success("cotationelement.followup.updated"))
                }
            }
        }
        render(RenderUtils.error("cotationelement.notupdated"))
    }
}
